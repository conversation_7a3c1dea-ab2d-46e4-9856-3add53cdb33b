import { Message } from '@/store/chat'
import { memo, useMemo } from 'react'

export const MessageChatImage = memo(({ message }: { message: Message }) => {
  const isHuman = message.type === 'human'
  let img = ''
  try {
    img = JSON.parse(JSON.parse(message.content).output)[0]
  } catch (error) {}

  return img ? (
    <div className={`flex rounded-[20px] ${isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2`}>
      <div
        className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
          isHuman ? 'bg-[#000000] text-white' : 'text-black'
        } ${isHuman ? 'flex flex-col items-end' : ''}`} // 添加样式
      >
        <div className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px] relative">
          <img className="w-[500px] h-[500px]" src={img} alt="" />
        </div>
      </div>
    </div>
  ) : null
})
