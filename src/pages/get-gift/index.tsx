import useObj<PERSON><PERSON> from '@/hooks/useObjAtom'
import { activePathState, userinfoState } from '@/store/global'
import { useState, useEffect } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { transferDetail, receiveTransferCoupon, TransferDetailRes } from '@/api/coupon'
import dayjs from 'dayjs'
import ConputItem from './components/ConpouItem'
import UseConputItem from './components/UseConpouItem'
import './index.scss'

const Index = () => {
  const userinfo = useObjAtom(userinfoState)
  const activePath = useObjAtom(activePathState)
  // 是否领取优惠券
  const [isGet, setGetVisible] = useState(false)
  const [conpouInfo, setCouponInfo] = useState<TransferDetailRes['data'] | null>(null)
  // 优惠券领取成功弹窗
  const [successVisible, setSuccessVisible] = useState(false)

  const router = useRouter()
  const { key = '' } = router.params

  // 获取优惠券详情
  const getCouponInfo = async (code) => {
    const res = await transferDetail(code)
    console.log('获取优惠券详情', res.data)
    setCouponInfo(res.data)
    if (res.data?.ticked == 0) {
      setGetVisible(false)
    } else {
      setGetVisible(true)
    }
  }

  // 领取优惠券
  const receiveCouponData = async () => {
    Taro.getStorage({
      key: '__weapp_open_id__', // 缓存数据的键名
      success: async (res) => {
        console.log('openid', res.data)
        const couponRes = await receiveTransferCoupon(key, res.data)
        console.log('领取好友发送的优惠券', couponRes)
        if (couponRes.code === 200) {
          getCouponInfo(key)
          setSuccessVisible(true)
        } else {
          Taro.showToast({
            title: couponRes.msg || '领取失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('获取失败:', err)
      },
      complete: () => {
        console.log('调用完成')
      }
    })
  }

  useEffect(() => {
    if (userinfo.val) {
      getCouponInfo(key)
    }
  }, [userinfo.val])

  return (
    <>
      <div className="bg">
        <div className="dialog">
          <div className="dialogTitle">好友礼赠</div>
          <div className="ribbon"></div>
          <div className="time">吉吉赠送于{dayjs(conpouInfo?.transferTime).format('MM月DD日HH:mm')}</div>
          {!isGet && conpouInfo && <ConputItem conpouInfo={conpouInfo}></ConputItem>}
          {!isGet && conpouInfo && (
            <div className="drawBtn" onClick={() => receiveCouponData()}>
              立即领取
            </div>
          )}
          {isGet && conpouInfo && <UseConputItem conpouInfo={conpouInfo}></UseConputItem>}
          {isGet && <div className="geted">代金券已被领完</div>}
        </div>

        {successVisible && conpouInfo && (
          <div className="shadowBg">
            <div className="dialogNoupou">
              <div className="hint">代金券领取成功</div>
              <div className="use">已放入「我的-优惠券」中</div>
              <div className="noupon">
                <div className="leftItem">
                  <div className="count">x{conpouInfo?.reduceNum}</div>
                  <div className="content">
                    <div className="moneyCount">
                      {conpouInfo.reducePrice / 100}
                      <span className="unit">元</span>
                    </div>
                    <div className="xianzhi">{conpouInfo?.couponName}</div>
                  </div>
                </div>
                <div className="rightItem">
                  <div className="title">{conpouInfo?.couponName}</div>
                  <div className="time">
                    {dayjs(conpouInfo?.endTime - conpouInfo?.startTime).format('HH : mm : ss')}
                    <span className="timeText">后过期</span>
                  </div>
                  <div className="useTitle">满{conpouInfo?.fullPrice / 100}金额可用</div>
                </div>
              </div>
              <div
                className="btn"
                onClick={() => {
                  Taro.switchTab({
                    url: '/pages/inspiration/index',
                    success: () => {
                      activePath.set('/pages/inspiration/index')
                    }
                  })
                }}
              >
                去使用
              </div>

              <div className="closeBtn" onClick={() => setSuccessVisible(false)}>
                ×
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default Index
