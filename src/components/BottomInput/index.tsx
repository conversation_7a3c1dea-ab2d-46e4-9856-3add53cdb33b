import Taro from '@tarojs/taro'
import useObjState from '@/hooks/useObjState'
import { getMiniStsToken } from '@/api/global'
import Keyboard from './components/Keyboard'
import Record from './components/Record'

interface PendingImage {
  url: string
  localPath: string
}

const Index = ({
  sendMessage,
  isTool = true,
  loading = false,
  cancelCurrentRun
}: {
  sendMessage: (message: string, type?: 'text' | 'image') => Promise<void> | void
  isTool?: boolean
  loading?: boolean
  cancelCurrentRun?: () => Promise<void>
}) => {
  const inputType = useObjState('text') // 用于记录输入类型，默认为文本输入
  const pendingImages = useObjState<PendingImage[]>([]) // 待发送的图片列表

  const chooseImage = (sourceType: string[]) => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: sourceType as any
    }).then(async (res) => {
      const filePath = res.tempFilePaths[0]
      console.log('filePath', filePath)
      if (!filePath) {
        return
      }

      // 显示上传中提示
      Taro.showLoading({ title: '上传中...', mask: true })

      try {
        const stsRes = await getMiniStsToken()
        const sts = stsRes.data
        console.log('sts', sts)

        const formData = {
          key: `ai-mp-wode-shop/${process.env.TARO_APP_ENV}/${Date.now()}.png`, // 上传文件名称
          policy: sts.policy, // 表单域
          'x-oss-signature-version': sts.x_oss_signature_version, // 指定签名的版本和算法
          'x-oss-credential': sts.x_oss_credential, // 指明派生密钥的参数集
          'x-oss-date': sts.x_oss_date, // 请求的时间
          'x-oss-signature': sts.signature, // 签名认证描述信息
          'x-oss-security-token': sts.security_token, // 安全令牌
          success_action_status: '200' // 上传成功后响应状态码
        }

        const uploadRes = await Taro.uploadFile({
          url: 'https://art-meta.oss-cn-hangzhou.aliyuncs.com',
          filePath: filePath,
          name: 'file', // 固定值为file
          formData: formData,
          withCredentials: false
        })

        Taro.hideLoading()

        if (uploadRes.statusCode === 200) {
          const imageUrl = `${sts.cdn_url}/${formData.key}`

          // 将图片添加到待发送列表，而不是直接发送
          pendingImages.set((prev) => [...prev, { url: imageUrl, localPath: filePath }])
        } else {
          console.error('Image upload failed:', uploadRes)
          Taro.showToast({ title: '图片上传失败', icon: 'error' })
        }
      } catch (error) {
        Taro.hideLoading()
        console.error('Image upload error:', error)
        Taro.showToast({ title: '图片上传失败', icon: 'error' })
      }
    })
  }

  // 删除待发送的图片
  const removeImage = (index: number) => {
    pendingImages.set((prev) => prev.filter((_, i) => i !== index))
  }

  // 发送消息（包含图片和文字）
  const handleSendMessage = (text: string) => {
    if (pendingImages.val.length > 0) {
      // 如果有图片，组合图片和文字发送
      const imageUrls = pendingImages.val.map((img) => `img:${img.url}`).join('\n')
      const combinedMessage = text ? `${imageUrls}\n${text}` : imageUrls
      sendMessage(combinedMessage, 'image')
      // 清空待发送图片
      pendingImages.set([])
    } else if (text.trim()) {
      // 只有文字
      sendMessage(text, 'text')
    }
  }

  return (
    <>
      {inputType.val === 'text' && (
        <Keyboard
          isTool={isTool}
          sendMessage={handleSendMessage}
          chooseImage={chooseImage}
          inputType={inputType}
          loading={loading}
          cancelCurrentRun={cancelCurrentRun}
          pendingImages={pendingImages.val}
          removeImage={removeImage}
        />
      )}
      {/* {inputType.val === 'record' && <Record sendMessage={sendMessage} chooseImage={chooseImage} inputType={inputType} />} */}
    </>
  )
}

export default Index
