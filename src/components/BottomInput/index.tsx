import Taro from '@tarojs/taro'
import useObjState from '@/hooks/useObjState'
import { getMiniStsToken } from '@/api/global'
import Keyboard from './components/Keyboard'
import Record from './components/Record'

const Index = ({
  sendMessage,
  isTool = true,
  loading = false,
  cancelCurrentRun
}: {
  sendMessage: (message: string, type?: 'text' | 'image') => Promise<void> | void
  isTool?: boolean
  loading?: boolean
  cancelCurrentRun?: () => Promise<void>
}) => {
  const inputType = useObjState('text') // 用于记录输入类型，默认为文本输入

  const chooseImage = (sourceType: string[]) => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: sourceType as any
    }).then(async (res) => {
      const filePath = res.tempFilePaths[0]
      console.log('filePath', filePath)
      if (!filePath) {
        return
      }
      const stsRes = await getMiniStsToken()
      const sts = stsRes.data
      console.log('sts', sts)

      const formData = {
        key: `ai-mp-wode-shop/${process.env.TARO_APP_ENV}/${Date.now()}.png`, // 上传文件名称
        policy: sts.policy, // 表单域
        'x-oss-signature-version': sts.x_oss_signature_version, // 指定签名的版本和算法
        'x-oss-credential': sts.x_oss_credential, // 指明派生密钥的参数集
        'x-oss-date': sts.x_oss_date, // 请求的时间
        'x-oss-signature': sts.signature, // 签名认证描述信息
        'x-oss-security-token': sts.security_token, // 安全令牌
        success_action_status: '200' // 上传成功后响应状态码
      }
      Taro.uploadFile({
        url: 'https://art-meta.oss-cn-hangzhou.aliyuncs.com',
        filePath: filePath,
        name: 'file', // 固定值为file
        formData: formData,
        withCredentials: false
      }).then((uploadRes) => {
        console.log('uploadRes', uploadRes)
        if (uploadRes.statusCode === 200) {
          const imageUrl = `${sts.cdn_url}/${formData.key}`
          const markdownImage = `img:${imageUrl}`

          sendMessage(markdownImage, 'image')
        } else {
          console.error('Image upload failed:', uploadRes)
        }
      })

      // const imageUrl = `https://img.newbrush.com/newbrush-agent/textImages/2025-07-09/175205320554738.png`
      // const markdownImage = `![Uploaded Image](${imageUrl})`
      // // 发送图片消息
      // sendMessage(markdownImage, 'image')
    })
  }

  return (
    <>
      {inputType.val === 'text' && (
        <Keyboard
          isTool={isTool}
          sendMessage={sendMessage}
          chooseImage={chooseImage}
          inputType={inputType}
          loading={loading}
          cancelCurrentRun={cancelCurrentRun}
        />
      )}
      {/* {inputType.val === 'record' && <Record sendMessage={sendMessage} chooseImage={chooseImage} inputType={inputType} />} */}
    </>
  )
}

export default Index
